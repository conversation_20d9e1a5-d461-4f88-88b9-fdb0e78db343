{"name": "loan-management-backend", "version": "1.0.0", "description": "Backend for Loan Management System", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "sequelize": "^6.35.0", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "qrcode": "^1.5.3"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}, "engines": {"node": ">=18.0.0"}}